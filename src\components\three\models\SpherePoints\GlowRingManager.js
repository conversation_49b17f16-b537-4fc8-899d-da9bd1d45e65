import * as THREE from "three";
import PlaneMarker from "./PlaneMarker.js";

/**
 * 扩散光环管理器
 *
 * 用于管理3D场景中的扩散光环，避免在相同位置重复创建光环。
 * 支持引用计数机制，确保光环被多个飞线使用时能正确管理生命周期。
 *
 * 功能特性：
 * - 基于位置的光环去重
 * - 引用计数管理
 * - 自动清理未使用的光环
 * - 支持不同类型的光环（起点、终点、自定义）
 * - 线程安全的操作
 *
 * 使用示例：
 * ```javascript
 * const manager = new GlowRingManager(scene);
 *
 * // 获取或创建光环
 * const startRing = manager.getOrCreateGlowRing(position, 'start', options);
 *
 * // 增加引用
 * manager.addReference(startRing, 'flight_123');
 *
 * // 移除引用
 * manager.removeReference(startRing, 'flight_123');
 * ```
 */
class GlowRingManager {
  constructor(scene) {
    this.scene = scene;

    // 光环存储映射：位置键 -> 光环数据
    this.glowRings = new Map();

    // 引用计数映射：光环ID -> 引用信息
    this.references = new Map();

    // 位置精度（用于生成位置键）
    this.positionPrecision = 6; // 小数点后6位精度

    // 调试模式
    this.debugMode = false;

    console.log("🌟 GlowRingManager 已初始化");
  }

  /**
   * 生成基于位置的唯一键
   * @param {THREE.Vector3} position - 3D位置
   * @returns {string} 位置键
   */
  generatePositionKey(position) {
    const precision = this.positionPrecision;
    const x = parseFloat(position.x.toFixed(precision));
    const y = parseFloat(position.y.toFixed(precision));
    const z = parseFloat(position.z.toFixed(precision));
    return `${x},${y},${z}`;
  }

  /**
   * 生成光环的唯一ID
   * @param {string} positionKey - 位置键
   * @param {string} type - 光环类型
   * @returns {string} 光环ID
   */
  generateGlowRingId(positionKey, type) {
    return `${type}_${positionKey}`;
  }

  /**
   * 检查指定位置和类型的光环是否已存在
   * @param {THREE.Vector3} position - 3D位置
   * @param {string} type - 光环类型 ('start', 'end', 'custom')
   * @returns {boolean} 是否存在
   */
  hasGlowRing(position, type) {
    const positionKey = this.generatePositionKey(position);
    const ringId = this.generateGlowRingId(positionKey, type);
    return this.glowRings.has(ringId);
  }

  /**
   * 获取或创建扩散光环
   * @param {THREE.Vector3} position - 3D位置
   * @param {string} type - 光环类型 ('start', 'end', 'custom')
   * @param {Object} options - 光环配置选项
   * @param {string} referenceId - 引用ID（通常是飞线ID）
   * @returns {THREE.Mesh} 光环对象
   */
  getOrCreateGlowRing(position, type, options = {}, referenceId = null) {
    const positionKey = this.generatePositionKey(position);
    const ringId = this.generateGlowRingId(positionKey, type);

    // 检查是否已存在
    if (this.glowRings.has(ringId)) {
      const ringData = this.glowRings.get(ringId);

      if (this.debugMode) {
        console.log(`🔄 复用现有光环: ${ringId} (位置: ${positionKey})`);
      }

      // 如果提供了引用ID，添加引用
      if (referenceId) {
        this.addReference(ringData.ring, referenceId);
      }

      return ringData.ring;
    }

    // 创建新光环
    let ring;
    const defaultOptions = {
      name: `GlowRing_${type}_${Date.now()}`,
      ...options,
    };

    switch (type) {
      case "start":
        ring = PlaneMarker.createStartMarker(position, defaultOptions);
        break;
      case "end":
        ring = PlaneMarker.createEndMarker(position, defaultOptions);
        break;
      case "custom":
        ring = PlaneMarker.createCustomMarker(position, defaultOptions);
        break;
      default:
        ring = PlaneMarker.createGlowRingMarker(position, defaultOptions);
    }

    // 存储光环数据
    const ringData = {
      ring: ring,
      type: type,
      position: position.clone(),
      positionKey: positionKey,
      ringId: ringId,
      createdAt: Date.now(),
      options: { ...defaultOptions },
    };

    this.glowRings.set(ringId, ringData);

    // 将ringId存储到光环对象的userData中，以便后续查找
    ring.userData.ringId = ringId;

    // 初始化引用计数
    this.references.set(ringId, {
      count: 0,
      referenceIds: new Set(),
      ringData: ringData,
    });

    // 如果提供了引用ID，添加引用
    if (referenceId) {
      this.addReference(ring, referenceId);
    }

    // 添加到场景
    if (this.scene) {
      this.scene.add(ring);
    }

    if (this.debugMode) {
      console.log(`✨ 创建新光环: ${ringId} (位置: ${positionKey}, 类型: ${type})`);
    }

    return ring;
  }

  /**
   * 添加光环引用
   * @param {THREE.Mesh} ring - 光环对象
   * @param {string} referenceId - 引用ID
   */
  addReference(ring, referenceId) {
    if (!ring || !ring.userData.isGlowRingMarker) {
      console.warn("🚨 GlowRingManager: 无效的光环对象");
      return false;
    }

    // 从光环对象获取ringId
    const ringId = this.getRingIdFromMesh(ring);
    if (!ringId) {
      console.warn("🚨 GlowRingManager: 无法获取光环ID");
      return false;
    }

    const refData = this.references.get(ringId);
    if (!refData) {
      console.warn(`🚨 GlowRingManager: 光环引用数据不存在: ${ringId}`);
      return false;
    }

    // 添加引用
    if (!refData.referenceIds.has(referenceId)) {
      refData.referenceIds.add(referenceId);
      refData.count++;

      if (this.debugMode) {
        console.log(`📌 添加光环引用: ${ringId} <- ${referenceId} (引用计数: ${refData.count})`);
      }
    }

    return true;
  }

  /**
   * 移除光环引用
   * @param {THREE.Mesh} ring - 光环对象
   * @param {string} referenceId - 引用ID
   * @returns {boolean} 是否成功移除
   */
  removeReference(ring, referenceId) {
    if (!ring || !ring.userData.isGlowRingMarker) {
      console.warn("🚨 GlowRingManager: 无效的光环对象");
      return false;
    }

    // 从光环对象获取ringId
    const ringId = this.getRingIdFromMesh(ring);
    if (!ringId) {
      console.warn("🚨 GlowRingManager: 无法获取光环ID");
      return false;
    }

    const refData = this.references.get(ringId);
    if (!refData) {
      console.warn(`🚨 GlowRingManager: 光环引用数据不存在: ${ringId}`);
      return false;
    }

    // 移除引用
    if (refData.referenceIds.has(referenceId)) {
      refData.referenceIds.delete(referenceId);
      refData.count--;

      if (this.debugMode) {
        console.log(`📌 移除光环引用: ${ringId} <- ${referenceId} (引用计数: ${refData.count})`);
      }

      // 如果引用计数为0，自动清理光环
      if (refData.count <= 0) {
        this.destroyGlowRing(ringId);
      }
    }

    return true;
  }

  /**
   * 从光环网格对象获取ringId
   * @param {THREE.Mesh} ring - 光环对象
   * @returns {string|null} 光环ID
   */
  getRingIdFromMesh(ring) {
    // 尝试从userData中获取
    if (ring.userData.ringId) {
      return ring.userData.ringId;
    }

    // 通过遍历查找（备用方法）
    for (const [ringId, ringData] of this.glowRings) {
      if (ringData.ring === ring) {
        // 缓存到userData中以提高后续查找效率
        ring.userData.ringId = ringId;
        return ringId;
      }
    }

    return null;
  }

  /**
   * 销毁指定的光环
   * @param {string} ringId - 光环ID
   */
  destroyGlowRing(ringId) {
    const ringData = this.glowRings.get(ringId);
    if (!ringData) {
      console.warn(`🚨 GlowRingManager: 光环不存在: ${ringId}`);
      return;
    }

    // 从场景中移除
    if (this.scene && ringData.ring.parent) {
      this.scene.remove(ringData.ring);
    }

    // 使用PlaneMarker的销毁方法清理资源
    PlaneMarker.destroyMarker(ringData.ring);

    // 清理管理器中的数据
    this.glowRings.delete(ringId);
    this.references.delete(ringId);

    if (this.debugMode) {
      console.log(`🗑️ 销毁光环: ${ringId}`);
    }
  }

  /**
   * 获取光环的引用计数
   * @param {THREE.Mesh} ring - 光环对象
   * @returns {number} 引用计数
   */
  getReferenceCount(ring) {
    const ringId = this.getRingIdFromMesh(ring);
    if (!ringId) return 0;

    const refData = this.references.get(ringId);
    return refData ? refData.count : 0;
  }

  /**
   * 获取光环的引用ID列表
   * @param {THREE.Mesh} ring - 光环对象
   * @returns {Array} 引用ID数组
   */
  getReferenceIds(ring) {
    const ringId = this.getRingIdFromMesh(ring);
    if (!ringId) return [];

    const refData = this.references.get(ringId);
    return refData ? Array.from(refData.referenceIds) : [];
  }

  /**
   * 清理所有未使用的光环
   */
  cleanupUnusedRings() {
    let cleanedCount = 0;

    for (const [ringId, refData] of this.references) {
      if (refData.count <= 0) {
        this.destroyGlowRing(ringId);
        cleanedCount++;
      }
    }

    if (this.debugMode && cleanedCount > 0) {
      console.log(`🧹 清理了 ${cleanedCount} 个未使用的光环`);
    }

    return cleanedCount;
  }

  /**
   * 获取管理器统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const totalRings = this.glowRings.size;
    const activeRings = Array.from(this.references.values()).filter((ref) => ref.count > 0).length;
    const unusedRings = totalRings - activeRings;

    return {
      totalRings,
      activeRings,
      unusedRings,
      totalReferences: Array.from(this.references.values()).reduce((sum, ref) => sum + ref.count, 0),
    };
  }

  /**
   * 启用/禁用调试模式
   * @param {boolean} enabled - 是否启用
   */
  setDebugMode(enabled) {
    this.debugMode = enabled;
    console.log(`🐛 GlowRingManager 调试模式: ${enabled ? "启用" : "禁用"}`);
  }

  /**
   * 销毁管理器（清理所有资源）
   */
  destroy() {
    // 销毁所有光环
    for (const ringId of this.glowRings.keys()) {
      this.destroyGlowRing(ringId);
    }

    // 清理数据结构
    this.glowRings.clear();
    this.references.clear();

    console.log("🗑️ GlowRingManager 已销毁");
  }
}

export default GlowRingManager;
