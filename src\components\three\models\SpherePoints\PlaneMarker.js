import * as THREE from "three";
import CustomShaderMaterial from "three-custom-shader-material/vanilla";

// Diffuse ring shader definition
const diffuseRingShader = {
  vertexShader: `
    // Vertex shader for expanding ring effect
    uniform float uTime;
    uniform float uRingRadius;
    uniform float uPulseSpeed;
    uniform float uPulseAmplitude;

    varying vec2 vUv;
    varying vec3 vWorldPos;
    varying vec3 vNormalDir;
    varying float vDistanceFromCenter;

    void main() {
      vUv = uv;
      vNormalDir = normalize(normalMatrix * normal);

      // Calculate world position
      vec4 worldPos = modelMatrix * vec4(position, 1.0);
      vWorldPos = worldPos.xyz;

      // Calculate distance from center for ring effect
      vDistanceFromCenter = length(position.xy);

      // No vertex animation - all expansion effects handled in fragment shader
      gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
  `,

  fragmentShader: `
    // Fragment shader for diffuse ring effect with expanding waves
    uniform float uTime;
    uniform vec3 uRingColor;
    uniform float uRingRadius;
    uniform float uRingWidth;
    uniform float uIntensity;
    uniform float uFadeDistance;
    uniform float uPulseSpeed;
    uniform float uPulseAmplitude;
    uniform float uOpacity;

    varying vec2 vUv;
    varying vec3 vWorldPos;
    varying vec3 vNormalDir;
    varying float vDistanceFromCenter;

    void main() {
      // Calculate distance from center (0,0) in UV space
      vec2 center = vec2(0.5, 0.5);
      float distanceFromCenter = length(vUv - center) * 2.0; // Normalize to 0-1 range

      // Create expanding wave effect instead of pulsing
      float waveTime = uTime * uPulseSpeed;
      float waveRadius = mod(waveTime, 2.0); // Wave expands from 0 to 2, then resets

      // Create multiple expanding rings for more dramatic effect
      float wave1 = waveRadius;
      float wave2 = mod(waveRadius + 0.5, 2.0); // Second wave offset by 0.5
      float wave3 = mod(waveRadius + 1.0, 2.0); // Third wave offset by 1.0

      // Calculate intensity for each wave
      float intensity1 = 0.0;
      float intensity2 = 0.0;
      float intensity3 = 0.0;

      // Wave 1 - 主要扩散环
      float dist1 = abs(distanceFromCenter - wave1);
      if (dist1 < uRingWidth) {
        float fadeOut = 1.0 - (waveRadius / 2.0); // Fade as wave expands
        fadeOut = pow(fadeOut, 0.8); // 减缓淡出速度，使环更持久
        intensity1 = (1.0 - smoothstep(0.0, uRingWidth * 0.8, dist1)) * fadeOut;
      }

      // Wave 2 - 第二个扩散环
      float dist2 = abs(distanceFromCenter - wave2);
      if (dist2 < uRingWidth) {
        float fadeOut2 = 1.0 - (mod(waveRadius + 0.5, 2.0) / 2.0);
        fadeOut2 = pow(fadeOut2, 0.8);
        intensity2 = (1.0 - smoothstep(0.0, uRingWidth * 0.8, dist2)) * fadeOut2 * 0.8; // 增加第二环强度
      }

      // Wave 3 - 第三个扩散环
      float dist3 = abs(distanceFromCenter - wave3);
      if (dist3 < uRingWidth) {
        float fadeOut3 = 1.0 - (mod(waveRadius + 1.0, 2.0) / 2.0);
        fadeOut3 = pow(fadeOut3, 0.8);
        intensity3 = (1.0 - smoothstep(0.0, uRingWidth * 0.8, dist3)) * fadeOut3 * 0.6; // 增加第三环强度
      }

      // Combine all waves
      float finalIntensity = max(max(intensity1, intensity2), intensity3) * uIntensity;

      // Add center glow that doesn't expand
      float centerGlow = 1.0 - smoothstep(0.0, 0.4, distanceFromCenter);
      centerGlow *= 0.4; // 增加中心发光强度

      // Combine expanding waves with center glow
      finalIntensity = max(finalIntensity, centerGlow);

      // Apply fresnel-like effect for better visibility
      vec3 viewDirection = normalize(cameraPosition - vWorldPos);
      float fresnel = 1.0 - abs(dot(vNormalDir, viewDirection));
      fresnel = pow(fresnel, 2.0);

      // Final color calculation
      float alpha = finalIntensity * uOpacity * (0.7 + 0.3 * fresnel);

      // Use CustomShaderMaterial's csm_DiffuseColor
      csm_DiffuseColor = vec4(uRingColor, alpha);
    }
  `,
};

/**
 * 平面标记工具类
 *
 * 用于在3D场景中创建各种类型的平面标记，如起点标记、终点标记等。
 * 支持自定义颜色、大小、透明度和朝向。
 * 现在默认使用CustomShaderMaterial的扩散光环效果，提供更好的视觉效果。
 * 支持光环管理器，避免重复创建相同位置的光环。
 *
 * 功能特性：
 * - 默认使用扩散光环效果，创建不断向外扩散的光圈动画
 * - 多重扩散波效果，增强视觉冲击力
 * - 可通过 useGlowEffect: false 禁用光环效果
 * - 支持动态属性更新和动画控制
 * - 兼容现有API，无需修改现有代码
 * - 自动资源管理和清理
 * - 支持光环管理器，避免重复创建相同位置的光环
 * - 引用计数机制，确保光环被多个飞线使用时正确管理
 *
 * 扩散效果说明：
 * - pulseSpeed: 控制扩散速度（值越小扩散越慢）
 * - ringWidth: 控制扩散环的宽度（值越小环越细）
 * - intensity: 控制扩散环的亮度强度
 * - ringRadius: 控制最大扩散范围
 *
 * 使用示例：
 * ```javascript
 * import PlaneMarker from './PlaneMarker.js';
 * import GlowRingManager from './GlowRingManager.js';
 *
 * // 创建光环管理器
 * const glowManager = new GlowRingManager(scene);
 * PlaneMarker.setGlowRingManager(glowManager);
 *
 * // 创建起点标记（自动使用管理器避免重复）
 * const startMarker = PlaneMarker.createStartMarker(startPosition, {}, 'flight_123');
 * scene.add(startMarker);
 *
 * // 创建传统样式标记（无扩散效果）
 * const basicMarker = PlaneMarker.createStartMarker(position, {
 *   useGlowEffect: false
 * });
 *
 * // 创建自定义扩散标记
 * const glowMarker = PlaneMarker.createGlowRingMarker(position, {
 *   ringColor: 0x00ffff,
 *   intensity: 1.5,
 *   pulseSpeed: 0.8, // 较慢的扩散
 *   ringWidth: 0.05  // 细扩散环
 * });
 *
 * // 动态更新属性
 * PlaneMarker.updateGlowRingProperties(glowMarker, {
 *   ringColor: 0xff0000,
 *   intensity: 2.0,
 *   pulseSpeed: 1.5  // 加快扩散速度
 * });
 * ```
 */
class PlaneMarker {
  // 静态光环管理器实例
  static glowRingManager = null;

  /**
   * 设置光环管理器
   * @param {GlowRingManager} manager - 光环管理器实例
   */
  static setGlowRingManager(manager) {
    this.glowRingManager = manager;
    console.log("🌟 PlaneMarker: 光环管理器已设置");
  }

  /**
   * 获取光环管理器
   * @returns {GlowRingManager|null} 光环管理器实例
   */
  static getGlowRingManager() {
    return this.glowRingManager;
  }

  /**
   * 创建起点标记平面
   * @param {THREE.Vector3} position - 平面位置
   * @param {Object} options - 配置选项
   * @param {string} referenceId - 引用ID（用于光环管理器）
   * @returns {THREE.Mesh} 平面网格对象
   */
  static createStartMarker(position, options = {}, referenceId = null) {
    const defaultOptions = {
      color: 0xff0000, // 红色
      ringColor: 0xff4444, // 稍亮的红色光环
      size: 0.5,
      opacity: 0.8,
      useGlowEffect: true, // 默认使用光环效果
      name: `StartPlane_${Date.now()}`,
    };

    const config = { ...defaultOptions, ...options };

    if (config.useGlowEffect) {
      // 如果有光环管理器，使用管理器创建
      if (this.glowRingManager) {
        return this.glowRingManager.getOrCreateGlowRing(
          position,
          "start",
          {
            ...config,
            ringRadius: 0.8, // 更大的扩散范围
            intensity: 2.2, // 更高的强度
            pulseSpeed: 0.8, // 较慢的扩散速度
            ringWidth: 0.2, // 更宽的扩散环
          },
          referenceId
        );
      } else {
        // 回退到直接创建
        return this.createGlowRingMarker(position, {
          ...config,
          ringRadius: 0.8, // 更大的扩散范围
          intensity: 2.2, // 更高的强度
          pulseSpeed: 0.8, // 较慢的扩散速度
          ringWidth: 0.2, // 更宽的扩散环
        });
      }
    } else {
      return this._createPlaneMarker(position, config);
    }
  }

  /**
   * 创建终点标记平面
   * @param {THREE.Vector3} position - 平面位置
   * @param {Object} options - 配置选项
   * @param {string} referenceId - 引用ID（用于光环管理器）
   * @returns {THREE.Mesh} 平面网格对象
   */
  static createEndMarker(position, options = {}, referenceId = null) {
    const defaultOptions = {
      color: 0x00ff00, // 绿色
      ringColor: 0x44ff44, // 稍亮的绿色光环
      size: 0.5,
      opacity: 0.8,
      useGlowEffect: true, // 默认使用光环效果
      name: `EndPlane_${Date.now()}`,
    };

    const config = { ...defaultOptions, ...options };

    if (config.useGlowEffect) {
      // 如果有光环管理器，使用管理器创建
      if (this.glowRingManager) {
        return this.glowRingManager.getOrCreateGlowRing(
          position,
          "end",
          {
            ...config,
            ringRadius: 0.8, // 更大的扩散范围
            intensity: 2.2, // 更高的强度
            pulseSpeed: 0.9, // 稍快的扩散速度（绿色终点）
            ringWidth: 0.2, // 更宽的扩散环
          },
          referenceId
        );
      } else {
        // 回退到直接创建
        return this.createGlowRingMarker(position, {
          ...config,
          ringRadius: 0.8, // 更大的扩散范围
          intensity: 2.2, // 更高的强度
          pulseSpeed: 0.9, // 稍快的扩散速度（绿色终点）
          ringWidth: 0.2, // 更宽的扩散环
        });
      }
    } else {
      return this._createPlaneMarker(position, config);
    }
  }

  /**
   * 创建自定义标记平面
   * @param {THREE.Vector3} position - 平面位置
   * @param {Object} options - 配置选项
   * @param {string} referenceId - 引用ID（用于光环管理器）
   * @returns {THREE.Mesh} 平面网格对象
   */
  static createCustomMarker(position, options = {}, referenceId = null) {
    const defaultOptions = {
      color: 0xffffff, // 白色
      ringColor: 0x88ddff, // 浅蓝色光环
      size: 0.5,
      opacity: 0.8,
      useGlowEffect: true, // 默认使用光环效果
      name: `CustomPlane_${Date.now()}`,
    };

    const config = { ...defaultOptions, ...options };

    if (config.useGlowEffect) {
      // 如果有光环管理器，使用管理器创建
      if (this.glowRingManager) {
        return this.glowRingManager.getOrCreateGlowRing(
          position,
          "custom",
          {
            ...config,
            ringRadius: 0.7, // 中等扩散范围
            intensity: 1.8, // 中等强度
            pulseSpeed: 1.2, // 中等扩散速度
            ringWidth: 0.18, // 更宽的扩散环
          },
          referenceId
        );
      } else {
        // 回退到直接创建
        return this.createGlowRingMarker(position, {
          ...config,
          ringRadius: 0.7, // 中等扩散范围
          intensity: 1.8, // 中等强度
          pulseSpeed: 1.2, // 中等扩散速度
          ringWidth: 0.18, // 更宽的扩散环
        });
      }
    } else {
      return this._createPlaneMarker(position, config);
    }
  }

  /**
   * 创建圆形标记平面
   * @param {THREE.Vector3} position - 平面位置
   * @param {Object} options - 配置选项
   * @returns {THREE.Mesh} 圆形平面网格对象
   */
  static createCircleMarker(position, options = {}) {
    const defaultOptions = {
      color: 0xffff00, // 黄色
      ringColor: 0xffff88, // 浅黄色光环
      radius: 0.25,
      segments: 32, // 增加分段数以获得更好的shader效果
      opacity: 0.8,
      useGlowEffect: true, // 默认使用光环效果
      name: `CirclePlane_${Date.now()}`,
    };

    const config = { ...defaultOptions, ...options };

    if (config.useGlowEffect) {
      // 使用光环效果
      return this.createGlowRingMarker(position, {
        ...config,
        size: config.radius * 2, // 将半径转换为尺寸
        ringRadius: 0.9, // 更大的扩散范围
        intensity: 2.0, // 更高的强度
        pulseSpeed: 1.1, // 较慢的扩散速度
        ringWidth: 0.22, // 更宽的扩散环
        segments: config.segments,
      });
    } else {
      // 使用传统材质
      const geometry = new THREE.CircleGeometry(config.radius, config.segments);

      const material = new THREE.MeshBasicMaterial({
        color: config.color,
        transparent: true,
        opacity: config.opacity,
        side: THREE.DoubleSide,
      });

      const plane = new THREE.Mesh(geometry, material);
      this._setPlanePositionAndOrientation(plane, position);
      plane.name = config.name;

      return plane;
    }
  }

  /**
   * 创建传统样式的标记（不使用光环效果）
   * @param {THREE.Vector3} position - 平面位置
   * @param {Object} options - 配置选项
   * @returns {THREE.Mesh} 传统样式的平面网格对象
   */
  static createBasicMarker(position, options = {}) {
    const defaultOptions = {
      color: 0xffffff,
      size: 0.5,
      opacity: 0.8,
      name: `BasicPlane_${Date.now()}`,
    };

    const config = { ...defaultOptions, ...options };
    return this._createPlaneMarker(position, config);
  }

  /**
   * 创建带扩散光环效果的标记
   * @param {THREE.Vector3} position - 平面位置
   * @param {Object} options - 配置选项
   * @param {boolean} bypassManager - 是否绕过管理器直接创建（内部使用）
   * @returns {THREE.Mesh} 带光环效果的平面网格对象
   */
  static createGlowRingMarker(position, options = {}, bypassManager = false) {
    // 如果有管理器且不绕过管理器，则使用管理器创建
    if (this.glowRingManager && !bypassManager) {
      return this.glowRingManager.getOrCreateGlowRing(position, "custom", options);
    }

    const defaultOptions = {
      color: 0xffffff, // 基础颜色
      ringColor: 0x00ffff, // 光环颜色
      size: 1.0, // 平面大小
      ringRadius: 0.6, // 光环半径 (0-1) - 现在用作最大扩散半径
      ringWidth: 0.25, // 光环宽度 - 增加以获得更明显的扩散环
      intensity: 2.0, // 光环强度 - 增加以获得更明显的效果
      fadeDistance: 0.3, // 淡出距离
      pulseSpeed: 1.0, // 扩散速度 - 降低以获得更慢的扩散
      pulseAmplitude: 0.1, // 保留用于兼容性，但在扩散效果中不使用
      opacity: 0.9, // 整体透明度 - 增加以获得更明显的效果
      segments: 32, // 几何体分段数（更高的值产生更平滑的效果）
      name: `GlowRingPlane_${Date.now()}`,
    };

    const config = { ...defaultOptions, ...options };

    // 创建圆形几何体（使用更多分段以获得更好的shader效果）
    const geometry = new THREE.CircleGeometry(config.size, config.segments);

    // 创建CustomShaderMaterial
    const material = new CustomShaderMaterial({
      baseMaterial: THREE.MeshBasicMaterial,
      vertexShader: diffuseRingShader.vertexShader,
      fragmentShader: diffuseRingShader.fragmentShader,
      uniforms: {
        uTime: { value: 0.0 },
        uRingColor: { value: new THREE.Color(config.ringColor) },
        uRingRadius: { value: config.ringRadius },
        uRingWidth: { value: config.ringWidth },
        uIntensity: { value: config.intensity },
        uFadeDistance: { value: config.fadeDistance },
        uPulseSpeed: { value: config.pulseSpeed },
        uPulseAmplitude: { value: config.pulseAmplitude },
        uOpacity: { value: config.opacity },
      },
      transparent: true,
      depthWrite: false,
      blending: THREE.AdditiveBlending, // 使用加法混合模式产生发光效果
      side: THREE.DoubleSide,
    });

    // 创建网格
    const plane = new THREE.Mesh(geometry, material);

    // 设置位置和朝向
    this._setPlanePositionAndOrientation(plane, position);

    // 设置名称
    plane.name = config.name;

    // 添加动画更新函数
    plane.onBeforeRender = () => {
      if (material.uniforms.uTime) {
        material.uniforms.uTime.value += 0.016; // 约60fps的时间增量
      }
    };

    // 存储配置以便后续更新
    plane.userData.glowConfig = config;
    plane.userData.isGlowRingMarker = true;

    return plane;
  }

  /**
   * 创建基础平面标记（内部方法）
   * @private
   * @param {THREE.Vector3} position - 平面位置
   * @param {Object} config - 配置对象
   * @returns {THREE.Mesh} 平面网格对象
   */
  static _createPlaneMarker(position, config) {
    // 创建平面几何体
    const geometry = new THREE.PlaneGeometry(config.size, config.size);

    // 创建平面材质
    const material = new THREE.MeshBasicMaterial({
      color: config.color,
      transparent: true,
      opacity: config.opacity,
      side: THREE.DoubleSide, // 双面显示
    });

    // 创建平面网格
    const plane = new THREE.Mesh(geometry, material);

    // 设置位置和朝向
    this._setPlanePositionAndOrientation(plane, position);

    // 设置名称
    plane.name = config.name;

    return plane;
  }

  /**
   * 设置平面位置和朝向（内部方法）
   * @private
   * @param {THREE.Mesh} plane - 平面对象
   * @param {THREE.Vector3} position - 位置向量
   */
  static _setPlanePositionAndOrientation(plane, position) {
    // 设置平面位置
    plane.position.copy(position);

    // 计算从地球中心指向位置的方向向量
    const direction = position.clone().normalize();

    // 让平面法向量指向地球中心外侧（朝向观察者）
    plane.lookAt(position.x + direction.x, position.y + direction.y, position.z + direction.z);
  }

  /**
   * 批量创建标记平面
   * @param {Array} positions - 位置数组
   * @param {Object} options - 配置选项
   * @returns {Array} 平面网格对象数组
   */
  static createMultipleMarkers(positions, options = {}) {
    const defaultOptions = {
      type: "custom", // 'start', 'end', 'custom', 'circle', 'glowRing', 'basic'
      color: 0xffffff,
      size: 0.5,
      opacity: 0.8,
      useGlowEffect: true, // 默认使用光环效果
    };

    const config = { ...defaultOptions, ...options };
    const markers = [];

    positions.forEach((position, index) => {
      const markerOptions = {
        ...config,
        name: `${config.type}Plane_${index}_${Date.now()}`,
      };

      let marker;
      switch (config.type) {
        case "start":
          marker = this.createStartMarker(position, markerOptions);
          break;
        case "end":
          marker = this.createEndMarker(position, markerOptions);
          break;
        case "circle":
          marker = this.createCircleMarker(position, markerOptions);
          break;
        case "glowRing":
          marker = this.createGlowRingMarker(position, markerOptions);
          break;
        case "basic":
          marker = this.createBasicMarker(position, markerOptions);
          break;
        default:
          marker = this.createCustomMarker(position, markerOptions);
      }

      markers.push(marker);
    });

    console.log(`🚀 PlaneMarker ~ createMultipleMarkers ~ 已创建 ${markers.length} 个标记`);
    return markers;
  }

  /**
   * 更新光环标记的属性
   * @param {THREE.Mesh} marker - 光环标记对象
   * @param {Object} properties - 要更新的属性
   */
  static updateGlowRingProperties(marker, properties = {}) {
    if (!marker || !marker.userData.isGlowRingMarker || !marker.material.uniforms) {
      console.warn("🚀 PlaneMarker ~ updateGlowRingProperties ~ 无效的光环标记对象");
      return;
    }

    const uniforms = marker.material.uniforms;

    // 更新可用的uniform属性
    if (properties.ringColor !== undefined) {
      uniforms.uRingColor.value.setHex(properties.ringColor);
    }
    if (properties.ringRadius !== undefined) {
      uniforms.uRingRadius.value = properties.ringRadius;
    }
    if (properties.ringWidth !== undefined) {
      uniforms.uRingWidth.value = properties.ringWidth;
    }
    if (properties.intensity !== undefined) {
      uniforms.uIntensity.value = properties.intensity;
    }
    if (properties.fadeDistance !== undefined) {
      uniforms.uFadeDistance.value = properties.fadeDistance;
    }
    if (properties.pulseSpeed !== undefined) {
      uniforms.uPulseSpeed.value = properties.pulseSpeed;
    }
    if (properties.pulseAmplitude !== undefined) {
      uniforms.uPulseAmplitude.value = properties.pulseAmplitude;
    }
    if (properties.opacity !== undefined) {
      uniforms.uOpacity.value = properties.opacity;
    }

    // 更新存储的配置
    Object.assign(marker.userData.glowConfig, properties);

    console.log(`🚀 PlaneMarker ~ updateGlowRingProperties ~ ${marker.name} 属性已更新:`, properties);
  }

  /**
   * 设置光环标记的动画状态
   * @param {THREE.Mesh} marker - 光环标记对象
   * @param {boolean} enabled - 是否启用动画
   */
  static setGlowRingAnimation(marker, enabled = true) {
    if (!marker || !marker.userData.isGlowRingMarker) {
      console.warn("🚀 PlaneMarker ~ setGlowRingAnimation ~ 无效的光环标记对象");
      return;
    }

    if (enabled) {
      // 启用动画
      marker.onBeforeRender = () => {
        if (marker.material.uniforms.uTime) {
          marker.material.uniforms.uTime.value += 0.016;
        }
      };
    } else {
      // 禁用动画
      marker.onBeforeRender = null;
    }

    console.log(`🚀 PlaneMarker ~ setGlowRingAnimation ~ ${marker.name} 动画${enabled ? "已启用" : "已禁用"}`);
  }

  /**
   * 更新标记颜色
   * @param {THREE.Mesh} marker - 标记对象
   * @param {number} color - 新颜色
   */
  static updateMarkerColor(marker, color) {
    if (marker && marker.material) {
      marker.material.color.setHex(color);
      console.log(`🚀 PlaneMarker ~ updateMarkerColor ~ ${marker.name} 颜色已更新为:`, color);
    }
  }

  /**
   * 更新标记透明度
   * @param {THREE.Mesh} marker - 标记对象
   * @param {number} opacity - 新透明度 (0-1)
   */
  static updateMarkerOpacity(marker, opacity) {
    if (marker && marker.material) {
      marker.material.opacity = Math.max(0, Math.min(1, opacity));
      console.log(`🚀 PlaneMarker ~ updateMarkerOpacity ~ ${marker.name} 透明度已更新为:`, opacity);
    }
  }

  /**
   * 移除光环引用（用于光环管理器）
   * @param {THREE.Mesh} marker - 光环标记对象
   * @param {string} referenceId - 引用ID
   * @returns {boolean} 是否成功移除
   */
  static removeGlowRingReference(marker, referenceId) {
    if (!marker || !marker.userData.isGlowRingMarker) {
      console.warn("🚨 PlaneMarker: 无效的光环标记对象");
      return false;
    }

    if (this.glowRingManager) {
      return this.glowRingManager.removeReference(marker, referenceId);
    } else {
      // 如果没有管理器，直接销毁（回退行为）
      this.destroyMarker(marker);
      return true;
    }
  }

  /**
   * 销毁标记（清理资源）
   * @param {THREE.Mesh} marker - 标记对象
   */
  static destroyMarker(marker) {
    if (marker) {
      // 停止动画（如果是光环标记）
      if (marker.userData.isGlowRingMarker) {
        marker.onBeforeRender = null;
      }

      // 清理几何体
      if (marker.geometry) {
        marker.geometry.dispose();
      }

      // 清理材质
      if (marker.material) {
        // 如果是CustomShaderMaterial，需要清理uniforms
        if (marker.material.uniforms) {
          Object.values(marker.material.uniforms).forEach((uniform) => {
            if (uniform.value && typeof uniform.value.dispose === "function") {
              uniform.value.dispose();
            }
          });
        }
        marker.material.dispose();
      }

      // 清理用户数据
      marker.userData = {};

      console.log(`🚀 PlaneMarker ~ destroyMarker ~ ${marker.name} 已销毁`);
    }
  }

  /**
   * 批量销毁标记
   * @param {Array} markers - 标记对象数组
   */
  static destroyMultipleMarkers(markers) {
    if (Array.isArray(markers)) {
      markers.forEach((marker) => this.destroyMarker(marker));
      console.log(`🚀 PlaneMarker ~ destroyMultipleMarkers ~ 已销毁 ${markers.length} 个标记`);
    }
  }

  /**
   * 获取光环标记的当前配置
   * @param {THREE.Mesh} marker - 光环标记对象
   * @returns {Object} 当前配置对象
   */
  static getGlowRingConfig(marker) {
    if (!marker || !marker.userData.isGlowRingMarker) {
      console.warn("🚀 PlaneMarker ~ getGlowRingConfig ~ 无效的光环标记对象");
      return null;
    }

    return { ...marker.userData.glowConfig };
  }
}

export default PlaneMarker;
