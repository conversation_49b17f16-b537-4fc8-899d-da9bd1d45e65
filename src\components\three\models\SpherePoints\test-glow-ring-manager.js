import * as THREE from "three";
import GlowRingManager from "./GlowRingManager.js";
import PlaneMarker from "./PlaneMarker.js";

/**
 * 光环管理器测试工具
 *
 * 用于测试光环管理器的功能，包括：
 * - 相同位置光环的去重
 * - 引用计数管理
 * - 自动清理未使用的光环
 * - 多个飞线共享同一个光环
 */
class GlowRingManagerTest {
  constructor() {
    this.scene = new THREE.Scene();
    this.manager = null;
    this.testResults = [];
  }

  /**
   * 初始化测试环境
   */
  init() {
    // 创建光环管理器
    this.manager = new GlowRingManager(this.scene);
    this.manager.setDebugMode(true);

    // 设置到PlaneMarker
    PlaneMarker.setGlowRingManager(this.manager);

    console.log("🧪 光环管理器测试环境已初始化");
  }

  /**
   * 测试相同位置光环的去重
   */
  testPositionDeduplication() {
    console.log("\n🧪 测试1: 相同位置光环去重");

    const position = new THREE.Vector3(1, 0, 0);

    // 创建第一个光环
    const ring1 = this.manager.getOrCreateGlowRing(position, "start", {}, "flight_1");

    // 在相同位置创建第二个光环
    const ring2 = this.manager.getOrCreateGlowRing(position, "start", {}, "flight_2");

    // 验证是否是同一个对象
    const isSameObject = ring1 === ring2;
    const stats = this.manager.getStats();

    this.testResults.push({
      test: "位置去重",
      passed: isSameObject && stats.totalRings === 1,
      details: `相同对象: ${isSameObject}, 总光环数: ${stats.totalRings}, 引用数: ${stats.totalReferences}`,
    });

    console.log(`✅ 相同位置光环去重测试: ${isSameObject ? "通过" : "失败"}`);
    console.log(`📊 统计信息:`, stats);
  }

  /**
   * 测试引用计数管理
   */
  testReferenceCount() {
    console.log("\n🧪 测试2: 引用计数管理");

    const position = new THREE.Vector3(2, 0, 0);

    // 创建光环并添加多个引用
    const ring = this.manager.getOrCreateGlowRing(position, "end", {}, "flight_3");
    this.manager.addReference(ring, "flight_4");
    this.manager.addReference(ring, "flight_5");

    const refCount = this.manager.getReferenceCount(ring);
    const refIds = this.manager.getReferenceIds(ring);

    const expectedCount = 3; // flight_3, flight_4, flight_5
    const countCorrect = refCount === expectedCount;
    const idsCorrect = refIds.length === expectedCount;

    this.testResults.push({
      test: "引用计数",
      passed: countCorrect && idsCorrect,
      details: `引用计数: ${refCount}/${expectedCount}, 引用ID数: ${refIds.length}/${expectedCount}`,
    });

    console.log(`✅ 引用计数测试: ${countCorrect && idsCorrect ? "通过" : "失败"}`);
    console.log(`📊 引用计数: ${refCount}, 引用ID: [${refIds.join(", ")}]`);
  }

  /**
   * 测试自动清理功能
   */
  testAutoCleanup() {
    console.log("\n🧪 测试3: 自动清理功能");

    const position = new THREE.Vector3(3, 0, 0);

    // 创建光环
    const ring = this.manager.getOrCreateGlowRing(position, "custom", {}, "flight_6");

    const statsBefore = this.manager.getStats();

    // 移除引用，应该触发自动清理
    this.manager.removeReference(ring, "flight_6");

    const statsAfter = this.manager.getStats();

    const wasCleanedUp = statsBefore.totalRings > statsAfter.totalRings;

    this.testResults.push({
      test: "自动清理",
      passed: wasCleanedUp,
      details: `清理前: ${statsBefore.totalRings}个光环, 清理后: ${statsAfter.totalRings}个光环`,
    });

    console.log(`✅ 自动清理测试: ${wasCleanedUp ? "通过" : "失败"}`);
    console.log(`📊 清理前后统计:`, { before: statsBefore, after: statsAfter });
  }

  /**
   * 测试PlaneMarker集成
   */
  testPlaneMarkerIntegration() {
    console.log("\n🧪 测试4: PlaneMarker集成");

    const position1 = new THREE.Vector3(4, 0, 0);
    const position2 = new THREE.Vector3(4, 0, 0); // 相同位置

    // 使用PlaneMarker创建光环
    const marker1 = PlaneMarker.createStartMarker(position1, {}, "flight_7");
    const marker2 = PlaneMarker.createStartMarker(position2, {}, "flight_8");

    const isSameObject = marker1 === marker2;
    const stats = this.manager.getStats();

    this.testResults.push({
      test: "PlaneMarker集成",
      passed: isSameObject,
      details: `相同对象: ${isSameObject}, 当前光环数: ${stats.totalRings}`,
    });

    console.log(`✅ PlaneMarker集成测试: ${isSameObject ? "通过" : "失败"}`);
    console.log(`📊 当前统计:`, stats);
  }

  /**
   * 测试不同类型光环的独立性
   */
  testDifferentTypes() {
    console.log("\n🧪 测试5: 不同类型光环独立性");

    const position = new THREE.Vector3(5, 0, 0);

    // 在相同位置创建不同类型的光环
    const startRing = this.manager.getOrCreateGlowRing(position, "start", {}, "flight_9");
    const endRing = this.manager.getOrCreateGlowRing(position, "end", {}, "flight_10");
    const customRing = this.manager.getOrCreateGlowRing(position, "custom", {}, "flight_11");

    const allDifferent = startRing !== endRing && endRing !== customRing && startRing !== customRing;
    const stats = this.manager.getStats();

    this.testResults.push({
      test: "不同类型独立性",
      passed: allDifferent,
      details: `三个光环都不同: ${allDifferent}, 总光环数: ${stats.totalRings}`,
    });

    console.log(`✅ 不同类型独立性测试: ${allDifferent ? "通过" : "失败"}`);
    console.log(`📊 当前统计:`, stats);
  }

  /**
   * 运行所有测试
   */
  runAllTests() {
    console.log("🚀 开始运行光环管理器测试套件");

    this.init();

    this.testPositionDeduplication();
    this.testReferenceCount();
    this.testAutoCleanup();
    this.testPlaneMarkerIntegration();
    this.testDifferentTypes();

    this.printTestResults();
  }

  /**
   * 打印测试结果
   */
  printTestResults() {
    console.log("\n📋 测试结果汇总:");
    console.log("=".repeat(50));

    let passedCount = 0;
    let totalCount = this.testResults.length;

    this.testResults.forEach((result, index) => {
      const status = result.passed ? "✅ 通过" : "❌ 失败";
      console.log(`${index + 1}. ${result.test}: ${status}`);
      console.log(`   详情: ${result.details}`);

      if (result.passed) passedCount++;
    });

    console.log("=".repeat(50));
    console.log(`📊 总体结果: ${passedCount}/${totalCount} 测试通过`);

    if (passedCount === totalCount) {
      console.log("🎉 所有测试都通过了！光环管理器工作正常。");
    } else {
      console.log("⚠️ 有测试失败，请检查光环管理器实现。");
    }

    // 打印最终统计信息
    const finalStats = this.manager.getStats();
    console.log("📈 最终统计信息:", finalStats);
  }

  /**
   * 清理测试环境
   */
  cleanup() {
    if (this.manager) {
      this.manager.destroy();
      this.manager = null;
    }
    console.log("🧹 测试环境已清理");
  }
}

// 导出测试类
export default GlowRingManagerTest;

// 如果在浏览器环境中，添加到全局对象以便手动测试
if (typeof window !== "undefined") {
  window.GlowRingManagerTest = GlowRingManagerTest;

  // 提供快速测试方法
  window.testGlowRingManager = () => {
    const test = new GlowRingManagerTest();
    test.runAllTests();
    return test;
  };
}
