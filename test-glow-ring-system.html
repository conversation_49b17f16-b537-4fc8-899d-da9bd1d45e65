<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>光环管理系统测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background-color: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #00ffff;
        }
        
        .test-button {
            background-color: #00ffff;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        
        .test-button:hover {
            background-color: #00cccc;
        }
        
        .test-results {
            background-color: #1a1a1a;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .success {
            color: #00ff00;
        }
        
        .error {
            color: #ff0000;
        }
        
        .info {
            color: #00ffff;
        }
        
        #canvas-container {
            width: 100%;
            height: 400px;
            background-color: #000;
            border-radius: 8px;
            margin: 20px 0;
            position: relative;
        }
        
        canvas {
            width: 100%;
            height: 100%;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 光环管理系统测试</h1>
        <p>这个页面用于测试扩散光环管理系统的功能，包括位置去重、引用计数和自动清理等特性。</p>
        
        <div class="test-section">
            <h2>📊 系统状态</h2>
            <div id="system-status" class="test-results">系统未初始化</div>
            <button class="test-button" onclick="initializeSystem()">初始化系统</button>
            <button class="test-button" onclick="getSystemStats()">获取统计信息</button>
        </div>
        
        <div class="test-section">
            <h2>🧪 基础功能测试</h2>
            <button class="test-button" onclick="testPositionDeduplication()">测试位置去重</button>
            <button class="test-button" onclick="testReferenceCount()">测试引用计数</button>
            <button class="test-button" onclick="testAutoCleanup()">测试自动清理</button>
            <button class="test-button" onclick="testDifferentTypes()">测试不同类型</button>
            <div id="basic-test-results" class="test-results"></div>
        </div>
        
        <div class="test-section">
            <h2>🎯 集成测试</h2>
            <button class="test-button" onclick="testPlaneMarkerIntegration()">测试PlaneMarker集成</button>
            <button class="test-button" onclick="testFlightLineScenario()">模拟飞线场景</button>
            <button class="test-button" onclick="runAllTests()">运行所有测试</button>
            <div id="integration-test-results" class="test-results"></div>
        </div>
        
        <div class="test-section">
            <h2>🎮 3D场景</h2>
            <div id="canvas-container">
                <canvas id="test-canvas"></canvas>
            </div>
            <button class="test-button" onclick="createTestScene()">创建测试场景</button>
            <button class="test-button" onclick="addTestRings()">添加测试光环</button>
            <button class="test-button" onclick="clearScene()">清空场景</button>
        </div>
        
        <div class="test-section">
            <h2>🔧 调试工具</h2>
            <button class="test-button" onclick="enableDebugMode()">启用调试模式</button>
            <button class="test-button" onclick="disableDebugMode()">禁用调试模式</button>
            <button class="test-button" onclick="cleanupUnusedRings()">清理未使用光环</button>
            <button class="test-button" onclick="resetSystem()">重置系统</button>
            <div id="debug-results" class="test-results"></div>
        </div>
    </div>

    <script type="module">
        import * as THREE from './node_modules/three/build/three.module.js';
        import GlowRingManager from './src/components/three/models/SpherePoints/GlowRingManager.js';
        import PlaneMarker from './src/components/three/models/SpherePoints/PlaneMarker.js';
        
        // 全局变量
        let scene, renderer, camera, glowRingManager;
        let testResults = [];
        
        // 初始化系统
        window.initializeSystem = function() {
            try {
                // 创建基础Three.js场景
                scene = new THREE.Scene();
                camera = new THREE.PerspectiveCamera(75, 800/400, 0.1, 1000);
                renderer = new THREE.WebGLRenderer({ canvas: document.getElementById('test-canvas') });
                renderer.setSize(800, 400);
                renderer.setClearColor(0x000011);
                
                camera.position.set(0, 0, 5);
                
                // 创建光环管理器
                glowRingManager = new GlowRingManager(scene);
                glowRingManager.setDebugMode(true);
                
                // 设置到PlaneMarker
                PlaneMarker.setGlowRingManager(glowRingManager);
                
                updateStatus('✅ 系统初始化成功', 'success');
                getSystemStats();
                
            } catch (error) {
                updateStatus(`❌ 系统初始化失败: ${error.message}`, 'error');
            }
        };
        
        // 更新状态显示
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('system-status');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            statusDiv.innerHTML += `<span class="${colorClass}">[${timestamp}] ${message}</span>\n`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
        }
        
        // 获取系统统计信息
        window.getSystemStats = function() {
            if (!glowRingManager) {
                updateStatus('❌ 系统未初始化', 'error');
                return;
            }
            
            const stats = glowRingManager.getStats();
            updateStatus(`📊 统计信息: 总光环=${stats.totalRings}, 活跃=${stats.activeRings}, 未使用=${stats.unusedRings}, 总引用=${stats.totalReferences}`, 'info');
        };
        
        // 测试位置去重
        window.testPositionDeduplication = function() {
            if (!glowRingManager) {
                updateTestResult('basic', '❌ 系统未初始化', 'error');
                return;
            }
            
            const position = new THREE.Vector3(1, 0, 0);
            const ring1 = glowRingManager.getOrCreateGlowRing(position, 'start', {}, 'test_flight_1');
            const ring2 = glowRingManager.getOrCreateGlowRing(position, 'start', {}, 'test_flight_2');
            
            const isSame = ring1 === ring2;
            const stats = glowRingManager.getStats();
            
            updateTestResult('basic', 
                `位置去重测试: ${isSame ? '✅ 通过' : '❌ 失败'} (相同对象: ${isSame}, 总光环: ${stats.totalRings})`,
                isSame ? 'success' : 'error'
            );
        };
        
        // 测试引用计数
        window.testReferenceCount = function() {
            if (!glowRingManager) {
                updateTestResult('basic', '❌ 系统未初始化', 'error');
                return;
            }
            
            const position = new THREE.Vector3(2, 0, 0);
            const ring = glowRingManager.getOrCreateGlowRing(position, 'end', {}, 'test_flight_3');
            glowRingManager.addReference(ring, 'test_flight_4');
            glowRingManager.addReference(ring, 'test_flight_5');
            
            const refCount = glowRingManager.getReferenceCount(ring);
            const expectedCount = 3;
            const isCorrect = refCount === expectedCount;
            
            updateTestResult('basic',
                `引用计数测试: ${isCorrect ? '✅ 通过' : '❌ 失败'} (计数: ${refCount}/${expectedCount})`,
                isCorrect ? 'success' : 'error'
            );
        };
        
        // 测试自动清理
        window.testAutoCleanup = function() {
            if (!glowRingManager) {
                updateTestResult('basic', '❌ 系统未初始化', 'error');
                return;
            }
            
            const position = new THREE.Vector3(3, 0, 0);
            const ring = glowRingManager.getOrCreateGlowRing(position, 'custom', {}, 'test_flight_6');
            
            const statsBefore = glowRingManager.getStats();
            glowRingManager.removeReference(ring, 'test_flight_6');
            const statsAfter = glowRingManager.getStats();
            
            const wasCleanedUp = statsBefore.totalRings > statsAfter.totalRings;
            
            updateTestResult('basic',
                `自动清理测试: ${wasCleanedUp ? '✅ 通过' : '❌ 失败'} (清理前: ${statsBefore.totalRings}, 清理后: ${statsAfter.totalRings})`,
                wasCleanedUp ? 'success' : 'error'
            );
        };
        
        // 测试不同类型
        window.testDifferentTypes = function() {
            if (!glowRingManager) {
                updateTestResult('basic', '❌ 系统未初始化', 'error');
                return;
            }
            
            const position = new THREE.Vector3(4, 0, 0);
            const startRing = glowRingManager.getOrCreateGlowRing(position, 'start', {}, 'test_flight_7');
            const endRing = glowRingManager.getOrCreateGlowRing(position, 'end', {}, 'test_flight_8');
            const customRing = glowRingManager.getOrCreateGlowRing(position, 'custom', {}, 'test_flight_9');
            
            const allDifferent = startRing !== endRing && endRing !== customRing && startRing !== customRing;
            
            updateTestResult('basic',
                `不同类型测试: ${allDifferent ? '✅ 通过' : '❌ 失败'} (三个光环都不同: ${allDifferent})`,
                allDifferent ? 'success' : 'error'
            );
        };
        
        // 更新测试结果
        function updateTestResult(section, message, type = 'info') {
            const resultDiv = document.getElementById(`${section}-test-results`);
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            resultDiv.innerHTML += `<span class="${colorClass}">[${timestamp}] ${message}</span>\n`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }
        
        // 运行所有测试
        window.runAllTests = function() {
            updateTestResult('integration', '🚀 开始运行所有测试...', 'info');
            
            setTimeout(() => testPositionDeduplication(), 100);
            setTimeout(() => testReferenceCount(), 200);
            setTimeout(() => testAutoCleanup(), 300);
            setTimeout(() => testDifferentTypes(), 400);
            setTimeout(() => {
                updateTestResult('integration', '✅ 所有基础测试完成', 'success');
                getSystemStats();
            }, 500);
        };
        
        // 启用调试模式
        window.enableDebugMode = function() {
            if (glowRingManager) {
                glowRingManager.setDebugMode(true);
                updateDebugResult('🐛 调试模式已启用', 'info');
            }
        };
        
        // 禁用调试模式
        window.disableDebugMode = function() {
            if (glowRingManager) {
                glowRingManager.setDebugMode(false);
                updateDebugResult('🔇 调试模式已禁用', 'info');
            }
        };
        
        // 更新调试结果
        function updateDebugResult(message, type = 'info') {
            updateTestResult('debug', message, type);
        }
        
        // 页面加载完成后自动初始化
        window.addEventListener('load', () => {
            updateStatus('📄 页面加载完成，请点击"初始化系统"开始测试', 'info');
        });
    </script>
</body>
</html>
